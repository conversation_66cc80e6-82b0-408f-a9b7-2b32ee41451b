<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">id</label>
        <el-input v-model="query.id" clearable placeholder="主键" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">Type</label>
        <el-select v-model="query.type" clearable size="small" placeholder="Type" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.information_type" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">展示位置</label>
        <el-select v-model="query.position" clearable size="small" placeholder="位置" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.information_position" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">Name</label>
        <el-input v-model="query.name" clearable placeholder="Name" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">Title</label>
        <el-input v-model="query.title" clearable placeholder="Title" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">Category</label>
        <el-select v-model="query.category" clearable size="small" placeholder="Category" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.information_category" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">推荐置顶</label>
        <el-select v-model="query.isRecommend" clearable size="small" placeholder="推荐置顶" class="filter-item" style="width: 185px" @change="crud.toQuery">
          <el-option v-for="item in dict.boolean" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        width="60%"
      >
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px">
          <el-divider content-position="left">基本信息</el-divider>
          <!-- 位置字段：单独占用一行 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="展示位置" prop="position">
                <el-select key="position-select" v-model="form.position" filterable placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in dict.information_position"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20"> <!-- 列间距20px -->
            <!-- 左列 -->
            <el-col :span="12">
              <!-- 项目类型：仅extension_home显示 -->
              <el-form-item v-if="isExtensionHome" label="Type" :prop="isExtensionHome ? 'type' : ''">
                <el-select :key="'type-select-' + form.position" v-model="form.type" filterable placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in dict.information_type"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <!-- 项目分类：仅当type为app时显示 -->
              <el-form-item v-if="form.type === 'DApp'" label="Category" :prop="form.type === 'DApp' ? 'category' : ''">
                <el-select
                  :key="'category-select-' + form.position"
                  v-model="form.category"
                  multiple
                  filterable
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.information_category"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <!-- 是否置顶：仅extension_home和discovery_blog显示 -->
              <el-form-item v-if="!isEventPosition" label="是否置顶">
                <el-select :key="'recommend-select-' + form.position" v-model="form.isRecommend" filterable placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in dict.boolean"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <!-- 排序值：所有类型都显示 -->
              <el-form-item label="Sort" prop="sort">
                <el-input v-model="form.sort" style="width: 100%" />
              </el-form-item>

              <!-- 标题：所有类型都显示 -->
              <el-form-item label="Title" prop="title">
                <el-input v-model="form.title" :rows="3" type="textarea" style="width: 100%" />
              </el-form-item>

              <!-- Tag字段：当type不为dapp时显示 -->
              <el-form-item v-if="isExtensionHome && form.type !== 'DApp'" label="Tag">
                <div style="display: flex; align-items: center">
                  <el-input
                    v-model="form.tag"
                    placeholder="只能上传jpg/png文件，且不超过5M"
                    style="width: 85%; margin-right: 5px"
                  />
                  <el-upload
                    ref="tagUpload"
                    style="display: inline-block"
                    :limit="1"
                    :before-upload="beforeUpload"
                    :action="informationImageUploadApi"
                    :show-file-list="false"
                    :headers="headers"
                    :data="{id: form.id}"
                    :on-success="handleSuccessTag"
                    :on-error="handleError"
                  >
                    <el-button size="small" type="success">点击上传</el-button>
                  </el-upload>
                </div>
              </el-form-item>

              <!-- 活动开始时间：discovery_blog和discovery_events显示 -->
              <el-form-item v-if="isEventPosition" label="活动开始时间" prop="activityStartTime">
                <el-date-picker
                  v-model="activityStartTimeFormatted"
                  type="datetime"
                  placeholder="选择活动开始时间 (UTC+0)"
                  style="width: 100%"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="utcPickerOptions"
                />
              </el-form-item>

              <!-- 活动结束时间：仅discovery_events显示 -->
              <el-form-item v-if="isEventPosition" label="活动结束时间" prop="activityEndTime">
                <el-date-picker
                  v-model="activityEndTimeFormatted"
                  type="datetime"
                  placeholder="选择活动结束时间 (UTC+0)"
                  style="width: 100%"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="utcPickerOptions"
                />
              </el-form-item>

              <!-- 活动举办方：仅discovery_events显示，移到右侧 -->
              <el-form-item v-if="isEventPosition" label="活动举办方" prop="organizer">
                <el-input v-model="form.organizer" style="width: 100%" />
              </el-form-item>

              <!-- 活动地点：仅discovery_events显示，移到右侧 -->
              <el-form-item v-if="isEventPosition" label="活动地点" prop="location">
                <el-input v-model="form.location" style="width: 100%" />
              </el-form-item>

              <el-form-item v-if="isDiscoveryBlog" label="创建日期" prop="created">
                <el-date-picker
                  v-model="form.created"
                  type="date"
                  placeholder="选择日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <!-- 右列 -->
            <el-col :span="12">
              <!-- DApp名称：仅extension_home显示，当type不为news时显示 -->
              <el-form-item v-if="isExtensionHome && form.type !== 'News'" label="Name">
                <el-input v-model="form.name" style="width: 100%" />
              </el-form-item>

              <!-- DApp logo：仅extension_home显示，当type不为news时显示 -->
              <el-form-item v-if="isExtensionHome && form.type !== 'News'" label="Logo">
                <div style="display: flex; align-items: center">
                  <el-input
                    v-model="form.logo"
                    placeholder="只能上传jpg/png文件，且不超过5M"
                    style="width: 85%; margin-right: 5px"
                  />
                  <el-upload
                    ref="logoUpload"
                    style="display: inline-block"
                    :limit="1"
                    :before-upload="beforeUpload"
                    :action="informationImageUploadApi"
                    :show-file-list="false"
                    :headers="headers"
                    :data="{id: form.id}"
                    :on-success="handleSuccessLogo"
                    :on-error="handleError"
                  >
                    <el-button size="small" type="success">点击上传</el-button>
                  </el-upload>
                </div>
              </el-form-item>

              <!-- 背景图片：所有类型都显示 -->
              <el-form-item label="Image">
                <div style="display: flex; align-items: center">
                  <el-input
                    v-model="form.image"
                    placeholder="只能上传jpg/png文件，且不超过5M"
                    style="width: 85%; margin-right: 5px"
                  />
                  <el-upload
                    ref="imageUpload"
                    style="display: inline-block"
                    :limit="1"
                    :before-upload="beforeUpload"
                    :action="informationImageUploadApi"
                    :show-file-list="false"
                    :headers="headers"
                    :data="{id: form.id}"
                    :on-success="handleSuccessImage"
                    :on-error="handleError"
                  >
                    <el-button size="small" type="success">点击上传</el-button>
                  </el-upload>
                </div>
              </el-form-item>

              <!-- 外部链接：所有类型都显示 -->
              <el-form-item v-if="!isDiscoveryBlog" label="Link" prop="link">
                <el-input v-model="form.link" style="width: 100%" />
              </el-form-item>

              <!-- 摘要：所有类型都显示 -->
              <el-form-item v-if="!isExtensionHome" label="摘要" :prop="!isExtensionHome ? 'summary' : ''">
                <el-input v-model="form.summary" :rows="8" type="textarea" style="width: 100%" />
              </el-form-item>

              <!-- Date字段：当type不为dapp时显示 -->
              <el-form-item v-if="isExtensionHome && form.type !== 'DApp'" label="Date" :prop="isExtensionHome && form.type !== 'DApp' ? 'date' : ''">
                <el-date-picker
                  v-model="form.date"
                  type="date"
                  placeholder="选择日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                />
              </el-form-item>

            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item v-if="isDiscoveryBlog" label="内容">
                <template>
                  <div ref="editor" style="border: 1px solid #ccc;">
                    <Toolbar
                      style="border-bottom: 1px solid #ccc"
                      :editor="editor"
                      :default-config="toolbarConfig"
                      :mode="editMode"
                    />
                    <Editor
                      v-model="form.content"
                      :style="{'height': editorHeight +'px', 'overflow-y': 'hidden'}"
                      :default-config="editorConfig"
                      :mode="editMode"
                      @onCreated="onCreated"
                    />
                  </div>
                </template>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 奖励信息区块：当Type为Article、News、YouTube、XPost时显示 -->
          <div v-if="shouldShowRewardInfo" style="margin-top: 20px;">
            <el-divider content-position="left">奖励信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否发放奖励" prop="isReward">
                  <el-radio-group v-model="form.isReward">
                    <el-radio label="N">N</el-radio>
                    <el-radio label="Y">Y</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="form.isReward === 'Y'" label="奖励分数" prop="rewardAmount">
                  <el-input
                    v-model="form.rewardAmount"
                    placeholder="请输入奖励分数（正整数）"
                    style="width: 100%"
                    type="number"
                    :min="1"
                    step="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- X Post回复规则区块：仅当Type为XPost时显示 -->
          <div v-if="shouldShowXPostRules" style="margin-top: 20px;">
            <el-divider content-position="left">X Post回复规则</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="提及@" prop="xMentionRequirement">
                  <el-input v-model="form.xMentionRequirement" placeholder="请输入提及要求" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="长度要求" prop="xLengthRequirement">
                  <el-input
                    v-model="form.xLengthRequirement"
                    placeholder="请输入长度要求（正整数）"
                    style="width: 100%"
                    type="number"
                    :min="1"
                    step="1"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="type" label="Type">
          <template slot-scope="scope">
            {{ dict.label.information_type[scope.row.type] }}
          </template>
        </el-table-column>
        <el-table-column prop="position" label="位置">
          <template slot-scope="scope">
            {{ dict.label.information_position[scope.row.position] }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="Name" />
        <!--        <el-table-column prop="logo" label="Logo" />-->
        <el-table-column prop="title" label="Title" />
        <!--        <el-table-column prop="subTitle" label="副标题" />-->
        <!--        <el-table-column prop="summary" label="摘要" />-->
        <!--        <el-table-column prop="content" label="内容" />-->
        <!--        <el-table-column prop="image" label="图片链接" />-->
        <!--        <el-table-column prop="link" label="Link" />-->
        <el-table-column prop="category" label="Category">
          <template slot-scope="scope">
            {{ formatCategory(scope.row.category) }}
          </template>
        </el-table-column>
        <el-table-column prop="isRecommend" label="推荐置顶">
          <template slot-scope="scope">
            {{ dict.label.boolean[scope.row.isRecommend] }}
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="Sort" />
        <!--        <el-table-column prop="organizer" label="活动举办方" />-->
        <!--        <el-table-column prop="location" label="活动地点" />-->
        <!--        <el-table-column prop="activityStartTime" label="活动开始时间" :formatter="formatTimestamp" />-->
        <!--        <el-table-column prop="activityEndTime" label="活动结束时间" :formatter="formatTimestamp" />-->
        <el-table-column prop="created" label="创建时间" :formatter="formatTime" />
        <el-table-column prop="modified" label="修改时间" :formatter="formatTime" />
        <!--        <el-table-column label="数据操作" align="center" width="270">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-button type="primary" size="mini" :disabled="scope.row.status === 1" @click="publishInformation(scope.row)">发布</el-button>-->
        <!--            <el-button type="danger" size="mini" :disabled="scope.row.status === 0" @click="withdrawInformation(scope.row)">撤回</el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudInformation, { publish, withdraw } from '@/api/drex/information/information'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { getToken } from '@/utils/auth'
import { mapGetters } from 'vuex'
import { Toolbar, Editor } from '@wangeditor/editor-for-vue'
import { upload } from '@/utils/upload'

const defaultForm = { xMentionRequirement: null, xLengthRequirement: null, id: null, type: null, position: null, name: null, logo: null, title: null, subTitle: null, summary: null, content: null, image: null, link: null, category: null, isRecommend: 'false', sort: null, tag: null, date: null, organizer: null, location: null, activityStartTime: null, activityEndTime: null, created: null, modified: null, isReward: 'N', rewardAmount: null, rewardRules: null }
export default {
  name: 'Information',
  components: { pagination, crudOperation, rrOperation, Toolbar, Editor },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['information_type', 'information_category', 'boolean', 'information_position'],
  cruds() {
    return CRUD({ title: '项目信息配置', url: 'api/information', idField: 'id', sort: 'id,desc', crudMethod: { ...crudInformation }})
  },
  props: {
    value: {
      type: String,
      required: false,
      default: ''
    },
    editorHeight: {
      type: Number,
      required: false,
      default: 420
    }
  },
  data() {
    const _this = this
    return {
      editMode: 'simple',
      editor: null,
      toolbarConfig: {},
      fullScreen: {
        enable: true,
        // 自定义全屏时的样式
        customStyle: `
            .w-e-full-screen-container {
              height: 700vh; /* 设置全屏时的高度 */
            }
          `
      },
      editorConfig: { placeholder: '请输入内容...', MENU_CONF: {
        'uploadImage': {
          // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
          allowedFileTypes: ['image/*'],
          // 自定义上传
          async customUpload(file, insertFn) { // JS 语法
            upload(_this.informationImageUploadApi, file).then(res => {
              console.info('上传成功:', res.data)
              insertFn(res.data, '', '')
            })
          }
        }
      }},
      permission: {
        add: ['admin', 'information:add'],
        edit: ['admin', 'information:edit'],
        del: ['admin', 'information:del']
      },
      headers: {
        'Authorization': getToken()
      },
      rules: {
        position: [
          { required: true, message: '位置不能为空', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        summary: [
          { required: true, message: '摘要不能为空', trigger: 'blur' }
        ],
        image: [
          { required: true, message: '背景图片不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '排序值不能为空', trigger: 'blur' }
        ],
        activityStartTime: [
          { required: true, message: '活动开始时间不能为空', trigger: 'blur' }
        ],
        activityEndTime: [
          { required: true, message: '活动结束时间不能为空', trigger: 'blur' }
        ],
        organizer: [
          { required: true, message: '活动举办方不能为空', trigger: 'blur' }
        ],
        location: [
          { required: true, message: '活动地点不能为空', trigger: 'blur' }
        ],
        link: [
          { required: true, message: '链接不能为空', trigger: 'blur' }
        ],
        created: [
          { required: true, message: '创建日期不能为空', trigger: 'blur' }
        ],
        rewardAmount: [
          { required: true, message: '奖励分数不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && (!/^\d+$/.test(value) || parseInt(value) <= 0)) {
                callback(new Error('奖励分数必须是正整数'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        xLengthRequirement: [
          {
            validator: (rule, value, callback) => {
              if (value && (!/^\d+$/.test(value) || parseInt(value) <= 0)) {
                callback(new Error('长度要求必须是正整数'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      queryTypeOptions: [
        { key: 'id', display_name: 'id' },
        { key: 'type', display_name: 'Type' },
        { key: 'position', display_name: '位置' },
        { key: 'name', display_name: 'Name' },
        { key: 'title', display_name: 'Title' },
        { key: 'category', display_name: 'Category' },
        { key: 'isRecommend', display_name: '推荐置顶' },
        { key: 'organizer', display_name: '活动举办方' },
        { key: 'location', display_name: '活动地点' }
      ],
      // UTC时区选择器配置
      utcPickerOptions: {
        // 禁用时区转换，强制使用UTC时间
        disabledDate: null,
        // 自定义时间选择器行为
        selectableRange: '00:00:00 - 23:59:59'
      }
    }
  },
  created() {
    // 初始化编辑器
    this.onCreated = this.onCreated.bind(this)
  },
  computed: {
    ...mapGetters([
      'informationImageUploadApi'
    ]),
    // 判断是否为extension_home
    isExtensionHome() {
      return this.form.position === 'extension_home'
    },
    // 判断是否为discovery_blog
    isDiscoveryBlog() {
      return this.form.position === 'discovery_blog'
    },
    // 判断是否为活动类型位置
    isEventPosition() {
      return this.form.position === 'discovery_events'
    },
    // 判断是否显示奖励信息（当Type为Article、News、YouTube、XPost时）
    shouldShowRewardInfo() {
      return ['Article', 'News', 'YouTube', 'XPost'].includes(this.form.type)
    },
    // 判断是否显示X Post回复规则（仅当Type为XPost时）
    shouldShowXPostRules() {
      return this.form.type === 'XPost'
    },
    // 活动开始时间的计算属性，确保日期选择器接收正确格式
    activityStartTimeFormatted: {
      get() {
        if (!this.form.activityStartTime) return null
        // 如果已经是字符串格式，直接返回
        if (typeof this.form.activityStartTime === 'string') {
          return this.form.activityStartTime
        }
        // 如果是时间戳，转换为字符串格式
        if (typeof this.form.activityStartTime === 'number') {
          return this.formatUtcTimestamp(this.form.activityStartTime)
        }
        return null
      },
      set(value) {
        if (!value) {
          this.form.activityStartTime = null
        } else {
          // 将字符串格式转换为时间戳存储
          this.form.activityStartTime = this.parseAsUtcTimestamp(value)
        }
      }
    },
    // 活动结束时间的计算属性，确保日期选择器接收正确格式
    activityEndTimeFormatted: {
      get() {
        if (!this.form.activityEndTime) return null
        // 如果已经是字符串格式，直接返回
        if (typeof this.form.activityEndTime === 'string') {
          return this.form.activityEndTime
        }
        // 如果是时间戳，转换为字符串格式
        if (typeof this.form.activityEndTime === 'number') {
          return this.formatUtcTimestamp(this.form.activityEndTime)
        }
        return null
      },
      set(value) {
        if (!value) {
          this.form.activityEndTime = null
        } else {
          // 将字符串格式转换为时间戳存储
          this.form.activityEndTime = this.parseAsUtcTimestamp(value)
        }
      }
    }
  },
  watch: {
    // 监听位置字段变化，清空不相关的字段并更新验证规则
    'form.position'(newPosition, oldPosition) {
      if (newPosition !== oldPosition) {
        // 根据位置类型清空不相关字段
        if (newPosition === 'extension_home') {
          // 切换到extension_home，清空活动相关字段和创建日期
          this.form.organizer = null
          this.form.location = null
          this.form.activityStartTime = null
          this.form.activityEndTime = null
          this.form.created = null
          // 重置奖励和X Post相关字段为默认值
          this.form.isReward = 'N'
          this.form.rewardAmount = null
          this.form.xMentionRequirement = null
          this.form.xLengthRequirement = null
        } else if (newPosition === 'discovery_blog') {
          // 切换到discovery_blog，清空DApp和活动部分字段
          this.form.type = null
          this.form.dappName = null
          this.form.dappLogo = null
          this.form.subTitle = null
          this.form.category = null
          this.form.organizer = null
          this.form.location = null
          this.form.activityStartTime = null
          this.form.activityEndTime = null
          // 重置奖励和X Post相关字段为默认值
          this.form.isReward = 'N'
          this.form.rewardAmount = null
          this.form.xMentionRequirement = null
          this.form.xLengthRequirement = null
        } else if (newPosition === 'discovery_events') {
          // 切换到discovery_events，清空DApp相关字段、项目类型、是否置顶和创建日期
          this.form.type = null
          this.form.dappName = null
          this.form.dappLogo = null
          this.form.subTitle = null
          this.form.content = null
          this.form.category = null
          this.form.isRecommend = 'false'
          this.form.created = null
          // 重置奖励和X Post相关字段为默认值
          this.form.isReward = 'N'
          this.form.rewardAmount = null
          this.form.xMentionRequirement = null
          this.form.xLengthRequirement = null
        }

        // 更新验证规则
        this.updateValidationRules()
      }
    },
    // 监听项目类型变化，清空奖励和X Post相关字段，以及条件字段
    'form.type'(newType, oldType) {
      if (newType !== oldType) {
        // 根据类型清空相关字段
        if (newType === 'News') {
          // 当类型为news时，清空name和logo字段
          this.form.name = null
          this.form.logo = null
        }
        if (newType === 'DApp') {
          // 当类型为dapp时，清空tag和date字段
          this.form.tag = null
          this.form.date = null
        }
        if (newType !== 'DApp') {
          // 当类型不为app时，清空category字段
          this.form.category = null
        }

        // 如果新类型不支持奖励信息，清空奖励相关字段
        if (!['Article', 'News', 'YouTube', 'XPost'].includes(newType)) {
          this.form.isReward = 'N'
          this.form.rewardAmount = null
          this.form.rewardRules = null
        }
        // 如果新类型不是XPost，清空X Post回复规则字段
        if (newType !== 'XPost') {
          this.form.xMentionRequirement = null
          this.form.xLengthRequirement = null
          this.form.rewardRules = null
        }
        // 更新验证规则
        this.updateValidationRules()
      }
    },
    // 监听是否发放奖励字段变化，动态更新验证规则
    'form.isReward'(newValue, oldValue) {
      if (newValue !== oldValue) {
        // 如果从Y改为N，清空奖励分数
        if (newValue !== 'Y') {
          this.form.rewardAmount = null
        }
        // 更新验证规则
        this.updateValidationRules()
      }
    }
  },
  mounted() {
    // 初始化验证规则
    this.updateValidationRules()
  },
  methods: {
    onCreated(editor) {
      console.info('WangEditor 编辑器已创建:', this.form.content)
      this.editor = Object.seal(editor)
      this.editor.setHtml(this.form.content)
    },
    // 更新验证规则
    updateValidationRules() {
      // 动态设置项目类型的验证规则
      if (this.isExtensionHome) {
        this.$set(this.rules, 'type', [
          { required: true, message: '项目类型不能为空', trigger: 'change' }
        ])
      } else {
        this.$delete(this.rules, 'type')
      }

      // 动态设置创建日期的验证规则
      if (this.isDiscoveryBlog) {
        this.$set(this.rules, 'created', [
          { required: true, message: '创建日期不能为空', trigger: 'blur' }
        ])
      } else {
        this.$delete(this.rules, 'created')
      }

      // 动态设置活动相关字段的验证规则
      if (this.isEventPosition) {
        // 为活动位置设置验证规则
        this.$set(this.rules, 'organizer', [
          { required: true, message: '活动举办方不能为空', trigger: 'blur' }
        ])
        this.$set(this.rules, 'location', [
          { required: true, message: '活动地点不能为空', trigger: 'blur' }
        ])
        this.$set(this.rules, 'activityStartTime', [
          { required: true, message: '活动开始时间不能为空', trigger: 'blur' }
        ])
        this.$set(this.rules, 'activityEndTime', [
          { required: true, message: '活动结束时间不能为空', trigger: 'blur' }
        ])
      } else {
        // 删除活动相关的验证规则
        this.$delete(this.rules, 'organizer')
        this.$delete(this.rules, 'location')
        this.$delete(this.rules, 'activityStartTime')
        this.$delete(this.rules, 'activityEndTime')
      }

      // 动态设置奖励分数的验证规则
      if (this.form.isReward === 'Y') {
        this.$set(this.rules, 'rewardAmount', [
          { required: true, message: '奖励分数不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && (!/^\d+$/.test(value) || parseInt(value) <= 0)) {
                callback(new Error('奖励分数必须是正整数'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ])
      } else {
        this.$delete(this.rules, 'rewardAmount')
      }

      // 动态设置X Post长度要求的验证规则
      if (this.form.type === 'XPost') {
        this.$set(this.rules, 'xLengthRequirement', [
          {
            validator: (rule, value, callback) => {
              if (value && (!/^\d+$/.test(value) || parseInt(value) <= 0)) {
                callback(new Error('长度要求必须是正整数'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ])
      } else {
        this.$delete(this.rules, 'xLengthRequirement')
      }

      // 清除表单验证状态，使用双重nextTick确保DOM完全更新
      this.$nextTick(() => {
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.clearValidate()
          }
        })
      })
    },
    // 钩子：添加前的处理
    [CRUD.HOOK.beforeToAdd](crud, form) {
      // 重置表单为默认值
      Object.assign(form, defaultForm)

      // 确保 category 字段是数组
      form.category = []

      // 设置默认值
      form.isRecommend = 'false'
      form.isReward = 'N'

      // 重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })

      return true
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    // 钩子：编辑前处理数据
    [CRUD.HOOK.beforeToEdit](crud, form) {
      // 处理 category 字段，将字符串转换为数组
      if (form.category && typeof form.category === 'string') {
        try {
          form.category = JSON.parse(form.category)
        } catch (e) {
          console.error('解析 category 字段失败:', e)
          form.category = []
        }
      }

      // 处理 date 字段，将时间戳或其他格式转换为日期字符串格式
      if (form.date) {
        if (typeof form.date === 'number' || (typeof form.date === 'string' && !isNaN(Number(form.date)))) {
          // 如果是时间戳，转换为 yyyy-MM-dd 格式
          const dateObj = new Date(Number(form.date))
          if (!isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear()
            const month = String(dateObj.getMonth() + 1).padStart(2, '0')
            const day = String(dateObj.getDate()).padStart(2, '0')
            form.date = `${year}-${month}-${day}`
          }
        }
      }

      // 确保 isReward 是字符串类型
      if (form.isReward !== null && form.isReward !== undefined) {
        form.isReward = String(form.isReward)
      }

      // 确保 rewardAmount 是字符串类型，用于表单显示
      if (form.rewardAmount !== null && form.rewardAmount !== undefined) {
        form.rewardAmount = String(form.rewardAmount)
      }

      // 优化 rewardRules 的解析逻辑
      if (form.rewardRules) {
        try {
          // 确保 rewardRules 是字符串格式
          const rulesStr = typeof form.rewardRules === 'string'
            ? form.rewardRules
            : JSON.stringify(form.rewardRules)

          const rules = JSON.parse(rulesStr)

          // 直接设置解析后的值
          form.xMentionRequirement = rules.xMention || null
          form.xLengthRequirement = rules.xLength || null
        } catch (e) {
          // 设置默认值
          console.error('解析 rewardRules 失败:', e)
          form.xMentionRequirement = null
          form.xLengthRequirement = null
        }
      } else {
        // 如果没有 rewardRules，设置为空值
        form.xMentionRequirement = null
        form.xLengthRequirement = null
      }

      // 时间字段的处理现在由计算属性负责，这里确保数据类型正确
      if (form.activityStartTime && typeof form.activityStartTime === 'string') {
        form.activityStartTime = Number(form.activityStartTime)
      }
      if (form.activityEndTime && typeof form.activityEndTime === 'string') {
        form.activityEndTime = Number(form.activityEndTime)
      }
      // 处理创建日期字段，将时间戳转换为日期字符串格式
      if (form.created) {
        if (typeof form.created === 'number' || (typeof form.created === 'string' && !isNaN(Number(form.created)))) {
          // 如果是时间戳，转换为 yyyy-MM-dd 格式
          const date = new Date(Number(form.created))
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            form.created = `${year}-${month}-${day}`
          }
        }
      }
      return form
    },
    // 钩子：编辑后处理
    [CRUD.HOOK.afterToEdit](crud, form) {
      // 确保 isReward 和 rewardAmount 值正确设置并触发视图更新
      if (form.isReward) {
        const originalIsReward = form.isReward
        const originalRewardAmount = form.rewardAmount

        // 先临时设为null触发视图更新
        this.$set(form, 'isReward', null)
        this.$set(form, 'rewardAmount', null)

        this.$nextTick(() => {
          // 再设回原始值
          this.$set(form, 'isReward', originalIsReward)
          this.$set(form, 'rewardAmount', originalRewardAmount)

          // 更新验证规则
          this.updateValidationRules()
        })
      } else {
        this.updateValidationRules()
      }
      // 更新验证规则
      this.$nextTick(() => {
        this.updateValidationRules()
      })
    },
    // 钩子：添加失败后的处理
    [CRUD.HOOK.afterAddError](crud, error) {
      // 保持显示当前错误状态，无需重置验证
      console.error('添加失败:', error)
    },
    // 钩子：编辑失败后的处理
    [CRUD.HOOK.afterEditError](crud, error) {
      // 保持显示当前错误状态，无需重置验证
      console.error('编辑失败:', error)
    },
    // 钩子：添加后的处理
    [CRUD.HOOK.afterToAdd](crud, form) {
      // 确保添加表单时，所有验证状态被重置
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    // 钩子：添加/编辑前处理数据
    [CRUD.HOOK.beforeToCU](crud, form) {
      // 处理 category 字段，确保它是数组
      if (form.category && typeof form.category === 'string') {
        try {
          form.category = JSON.parse(form.category)
        } catch (e) {
          console.error('解析 category 字段失败:', e)
          form.category = []
        }
      }
      // 时间字段的转换现在由计算属性的 setter 自动处理
      // 这里确保时间字段是数字类型（时间戳）
      return form
    },
    // 钩子：表单提交前处理数据
    [CRUD.HOOK.beforeSubmit](crud) {
      // 转换排序值为数字类型
      if (crud.form.sort !== null && crud.form.sort !== undefined) {
        crud.form.sort = Number(crud.form.sort)
      }

      // 处理创建日期字段，将日期字符串转换为时间戳
      if (crud.form.created && typeof crud.form.created === 'string') {
        // 如果是 yyyy-MM-dd 格式，转换为时间戳
        if (crud.form.created.match(/^\d{4}-\d{2}-\d{2}$/)) {
          const date = new Date(crud.form.created + ' 00:00:00')
          if (!isNaN(date.getTime())) {
            crud.form.created = date.getTime()
          }
        }
      }

      // 如果是extension_home位置，必须选择项目类型
      if (crud.form.position === 'extension_home') {
        if (!crud.form.type) {
          this.$message({
            message: '项目类型不能为空',
            type: 'warning'
          })
          return false
        }
      }
      if (crud.form.position === 'discovery_blog') {
        if (!crud.form.link) {
          this.$message({
            message: '外部链接不能为空',
            type: 'warning'
          })
          return false
        }
        if (!crud.form.created) {
          this.$message({
            message: '创建日期不能为空',
            type: 'warning'
          })
          return false
        }
      }
      // 如果是discovery_events位置，验证活动相关必填字段
      if (crud.form.position === 'discovery_events') {
        if (!crud.form.location) {
          this.$message({
            message: '活动地点不能为空',
            type: 'warning'
          })
          return false
        }

        if (!crud.form.organizer) {
          this.$message({
            message: '活动举办方不能为空',
            type: 'warning'
          })
          return false
        }
      }

      // 验证奖励相关字段
      if (crud.form.isReward === 'Y' && !crud.form.rewardAmount) {
        this.$message({
          message: '奖励分数不能为空',
          type: 'warning'
        })
        return false
      }

      // 处理规则字段 - 将UI表单字段合并为JSON
      if (crud.form.type === 'XPost') {
        // 创建rewardRules对象
        const rules = {}

        // 添加提及规则
        if (crud.form.xMentionRequirement) {
          rules.xMention = crud.form.xMentionRequirement
        }

        // 添加长度规则
        if (crud.form.xLengthRequirement) {
          rules.xLength = crud.form.xLengthRequirement
        }

        // 只有当有规则时才设置rewardRules字段
        if (Object.keys(rules).length > 0) {
          crud.form.rewardRules = JSON.stringify(rules)
        } else {
          crud.form.rewardRules = null
        }
      } else {
        crud.form.rewardRules = null
      }

      // 如果 category 是数组，则不需要转换，因为服务端会处理
      // 这里仅做日志记录，以便调试
      if (crud.form.category && Array.isArray(crud.form.category)) {
        console.log('提交的 category 数据:', crud.form.category)
      }
      return true
    },
    publishInformation(row) {
      this.$confirm(`确定发布项目信息 ${row.type} ${row.title} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        publish(row.id).then(result => {
          this.crud.notify(result, 'success')
          this.crud.resetQuery()
        })
      })
    },
    withdrawInformation(row) {
      this.$confirm(`确定撤回项目信息 ${row.type} ${row.title} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        withdraw(row.id).then(result => {
          this.crud.notify(result, 'success')
          this.crud.resetQuery()
        })
      })
    },
    formatTime(row, column, cellValue) {
      if (!cellValue) return '-' // 处理空值
      const date = new Date(cellValue)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false // 24小时制
      }).replace(/\//g, '-') // 将 / 替换为 -
    },
    formatTimestamp(row, column, cellValue) {
      if (!cellValue) return '-' // 处理空值
      const date = new Date(cellValue)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false // 24小时制
      }).replace(/\//g, '-') // 将 / 替换为 -
    },
    beforeUpload(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 5MB!')
      }
      return isLt2M
    },
    handleSuccessLogo(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.logo = response
      // 重置上传组件，以便下次上传
      this.$refs.logoUpload.clearFiles()
    },
    handleSuccessImage(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.image = response
      // 重置上传组件，以便下次上传
      this.$refs.imageUpload.clearFiles()
    },
    handleSuccessTag(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.tag = response
      // 重置上传组件，以便下次上传
      this.$refs.tagUpload.clearFiles()
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    },
    // 格式化Category字段
    formatCategory(category) {
      if (!category) return '-'

      // 如果是字符串形式的数组，尝试解析
      if (typeof category === 'string') {
        try {
          const parsedCategory = JSON.parse(category)
          if (Array.isArray(parsedCategory)) {
            return parsedCategory.join(', ')
          }
          return category
        } catch (e) {
          return category
        }
      }

      // 如果已经是数组，直接连接
      if (Array.isArray(category)) {
        return category.join(', ')
      }

      return String(category)
    },

    // 将时间字符串解析为UTC时间戳
    parseAsUtcTimestamp(timeString) {
      if (!timeString) return null

      // 如果是数字（时间戳），直接返回
      if (typeof timeString === 'number') {
        return timeString
      }

      // 如果是日期对象，转为字符串
      if (timeString instanceof Date) {
        const year = timeString.getFullYear()
        const month = String(timeString.getMonth() + 1).padStart(2, '0')
        const day = String(timeString.getDate()).padStart(2, '0')
        const hour = String(timeString.getHours()).padStart(2, '0')
        const minute = String(timeString.getMinutes()).padStart(2, '0')
        const second = String(timeString.getSeconds()).padStart(2, '0')
        timeString = `${year}-${month}-${day} ${hour}:${minute}:${second}`
      }

      // 确保是字符串
      if (typeof timeString !== 'string') {
        console.error('无效的日期格式:', timeString, '类型:', typeof timeString)
        return null
      }

      // 解析时间字符串 "YYYY-MM-DD HH:mm:ss"
      const parts = timeString.match(/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/)
      if (!parts) {
        console.error('时间字符串格式不匹配:', timeString)
        return null
      }

      // 创建UTC Date对象
      const utcDate = new Date(Date.UTC(
        parseInt(parts[1]), // year
        parseInt(parts[2]) - 1, // month (0-based)
        parseInt(parts[3]), // day
        parseInt(parts[4]), // hour
        parseInt(parts[5]), // minute
        parseInt(parts[6]) // second
      ))

      return utcDate.getTime()
    },
    // 将UTC时间戳转换为显示字符串
    formatUtcTimestamp(timestamp) {
      if (!timestamp) return ''

      // 确保时间戳是数字类型，支持 long 类型
      if (typeof timestamp === 'string') {
        timestamp = Number(timestamp)
      }

      // 检查时间戳是否有效
      if (isNaN(timestamp) || timestamp <= 0) {
        console.warn('无效的时间戳:', timestamp)
        return ''
      }

      const date = new Date(timestamp)

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无法解析的时间戳:', timestamp)
        return ''
      }

      const year = date.getUTCFullYear()
      const month = String(date.getUTCMonth() + 1).padStart(2, '0')
      const day = String(date.getUTCDate()).padStart(2, '0')
      const hour = String(date.getUTCHours()).padStart(2, '0')
      const minute = String(date.getUTCMinutes()).padStart(2, '0')
      const second = String(date.getUTCSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    }
  }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style scoped>
  ::v-deep .w-e-text-container {
    height: 100% !important;
  }
</style>
